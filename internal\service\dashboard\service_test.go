package dashboard

import (
	"context"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Mock repository
type MockDashboardRepository struct {
	mock.Mock
}

func (m *MockDashboardRepository) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialMap), args.Error(1)
}

func (m *MockDashboardRepository) SaveFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error {
	args := m.Called(ctx, financialMap)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error {
	args := m.Called(ctx, financialMap)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindIncomeSource(ctx context.Context, id primitive.ObjectID) (*dashboard.IncomeSource, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardRepository) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardRepository) CreateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	args := m.Called(ctx, incomeSource)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	args := m.Called(ctx, incomeSource)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteIncomeSource(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindEmergencyFund(ctx context.Context, userID string) (*dashboard.EmergencyFund, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.EmergencyFund), args.Error(1)
}

func (m *MockDashboardRepository) CreateEmergencyFund(ctx context.Context, emergencyFund *dashboard.EmergencyFund) error {
	args := m.Called(ctx, emergencyFund)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateEmergencyFund(ctx context.Context, emergencyFund *dashboard.EmergencyFund) error {
	args := m.Called(ctx, emergencyFund)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindInvestment(ctx context.Context, id primitive.ObjectID) (*dashboard.Investment, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardRepository) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardRepository) CreateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	args := m.Called(ctx, investment)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	args := m.Called(ctx, investment)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteInvestment(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindAsset(ctx context.Context, id primitive.ObjectID) (*dashboard.Asset, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardRepository) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardRepository) CreateAsset(ctx context.Context, asset *dashboard.Asset) error {
	args := m.Called(ctx, asset)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateAsset(ctx context.Context, asset *dashboard.Asset) error {
	args := m.Called(ctx, asset)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteAsset(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	args := m.Called(ctx, userID, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.NetWorthSnapshot), args.Error(1)
}

func (m *MockDashboardRepository) SaveNetWorthSnapshot(ctx context.Context, snapshot *dashboard.NetWorthSnapshot) error {
	args := m.Called(ctx, snapshot)
	return args.Error(0)
}

// Mock financial sheet service
type MockFinancialSheetService struct {
	mock.Mock
}

func (m *MockFinancialSheetService) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, year int, month int) ([]*financialsheet.Transaction, error) {
	args := m.Called(ctx, userID, categoryType, year, month)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*financialsheet.Transaction), args.Error(1)
}

func (m *MockFinancialSheetService) CountUserCategories(ctx context.Context, userID string) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

// Stub implementations for methods not used in dashboard service but required by interface
func (m *MockFinancialSheetService) Find(ctx context.Context, id string) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindByUserAndPeriod(ctx context.Context, userID string, year, month int, flatten bool) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) Update(ctx context.Context, record *financialsheet.Record) error {
	return nil
}

func (m *MockFinancialSheetService) Delete(ctx context.Context, id string) error {
	return nil
}

func (m *MockFinancialSheetService) CreateCategory(ctx context.Context, category *financialsheet.Category, userID string) (*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindCategory(ctx context.Context, id string) (*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindAllCategoriesByUser(ctx context.Context, userID string) ([]*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindCategoryByIdentifier(ctx context.Context, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) UpdateCategory(ctx context.Context, category *financialsheet.Category) error {
	return nil
}

func (m *MockFinancialSheetService) DeleteCategory(ctx context.Context, id string, userID string) error {
	return nil
}

func (m *MockFinancialSheetService) CreateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) CreateDreamTransaction(ctx context.Context, userID string, dreamID string, amount monetary.Amount) error {
	return nil
}

func (m *MockFinancialSheetService) FindTransaction(ctx context.Context, recordID string, transactionID string) (*financialsheet.Transaction, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) UpdateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) DeleteTransaction(ctx context.Context, record *financialsheet.Record, transactionID string) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) Initialize(ctx context.Context, userID string, userName string) error {
	return nil
}

// Test suite
type DashboardServiceTestSuite struct {
	suite.Suite
	service                   Service
	mockRepo                  *MockDashboardRepository
	mockFinancialSheetService *MockFinancialSheetService
	testUserID                string
}

func (suite *DashboardServiceTestSuite) SetupTest() {
	suite.mockRepo = new(MockDashboardRepository)
	suite.mockFinancialSheetService = new(MockFinancialSheetService)
	suite.service = New(suite.mockRepo, suite.mockFinancialSheetService)
	suite.testUserID = "test-user-123"
}

func TestDashboardServiceTestSuite(t *testing.T) {
	suite.Run(t, new(DashboardServiceTestSuite))
}

func (suite *DashboardServiceTestSuite) TestCreateIncomeSource() {
	ctx := context.Background()

	suite.mockRepo.On("CreateIncomeSource", ctx, mock.AnythingOfType("*dashboard.IncomeSource")).Return(nil)

	incomeSource, err := suite.service.CreateIncomeSource(ctx, suite.testUserID, "Salary", 5000, financialsheet.MoneySourceOpt1)

	suite.Require().NoError(err)
	suite.Equal("Salary", incomeSource.Name)
	suite.Equal(monetary.Amount(5000), incomeSource.MonthlyAmount)
	suite.Equal(suite.testUserID, incomeSource.UserID)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestCreateIncomeSource_ValidationError() {
	ctx := context.Background()

	// Test with empty name
	_, err := suite.service.CreateIncomeSource(ctx, suite.testUserID, "", 5000, financialsheet.MoneySourceOpt1)

	suite.Error(err)
	suite.Contains(err.Error(), "income source name is required")
}

func (suite *DashboardServiceTestSuite) TestUpdateEmergencyFund_CreateNew() {
	ctx := context.Background()

	// Mock repository to return NotFound error (emergency fund doesn't exist)
	notFoundErr := errors.New(errors.Repository, "emergency fund not found", errors.NotFound, nil)
	suite.mockRepo.On("FindEmergencyFund", ctx, suite.testUserID).Return(nil, notFoundErr)
	suite.mockRepo.On("CreateEmergencyFund", ctx, mock.AnythingOfType("*dashboard.EmergencyFund")).Return(nil)

	err := suite.service.UpdateEmergencyFund(ctx, suite.testUserID, 10000)

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestUpdateEmergencyFund_UpdateExisting() {
	ctx := context.Background()

	existingFund := &dashboard.EmergencyFund{
		ObjectID:     primitive.NewObjectID(),
		UserID:       suite.testUserID,
		CurrentValue: 5000,
		GoalValue:    50000,
	}

	suite.mockRepo.On("FindEmergencyFund", ctx, suite.testUserID).Return(existingFund, nil)
	suite.mockRepo.On("UpdateEmergencyFund", ctx, mock.AnythingOfType("*dashboard.EmergencyFund")).Return(nil)

	err := suite.service.UpdateEmergencyFund(ctx, suite.testUserID, 15000)

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestCreateMonthlySnapshot() {
	ctx := context.Background()

	// Mock data
	emergencyFund := &dashboard.EmergencyFund{
		UserID:       suite.testUserID,
		CurrentValue: 10000,
		GoalValue:    50000,
	}

	investments := []*dashboard.Investment{
		{UserID: suite.testUserID, Name: "Investment 1", CurrentValue: 15000},
		{UserID: suite.testUserID, Name: "Investment 2", CurrentValue: 10000},
	}

	assets := []*dashboard.Asset{
		{UserID: suite.testUserID, Description: "Car", Value: 80000},
	}

	// Setup mocks
	suite.mockRepo.On("FindEmergencyFund", ctx, suite.testUserID).Return(emergencyFund, nil)
	suite.mockRepo.On("FindInvestments", ctx, suite.testUserID).Return(investments, nil)
	suite.mockRepo.On("FindAssets", ctx, suite.testUserID).Return(assets, nil)
	suite.mockRepo.On("SaveNetWorthSnapshot", ctx, mock.AnythingOfType("*dashboard.NetWorthSnapshot")).Return(nil)

	err := suite.service.CreateMonthlySnapshot(ctx, suite.testUserID)

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestAggregateIncomeSourcesFromTransactions() {
	ctx := context.Background()

	// Create mock transactions with different money sources for the same category
	transactions := []*financialsheet.Transaction{
		{
			Category:    financialsheet.CategoryIdentifierBenefits,
			MoneySource: financialsheet.MoneySourceOpt2, // Should be "Bolsa Família"
			Value:       10000,
			Type:        financialsheet.CategoryTypeIncome,
		},
		{
			Category:    financialsheet.CategoryIdentifierBenefits,
			MoneySource: financialsheet.MoneySourceOpt2, // Should be "Bolsa Família"
			Value:       10000,
			Type:        financialsheet.CategoryTypeIncome,
		},
		{
			Category:    financialsheet.CategoryIdentifierBenefits,
			MoneySource: financialsheet.MoneySourceOpt2, // Should be "Bolsa Família"
			Value:       10000,
			Type:        financialsheet.CategoryTypeIncome,
		},
		{
			Category:    financialsheet.CategoryIdentifierCompensation,
			MoneySource: financialsheet.MoneySourceOpt1, // Should be "Salário CLT"
			Value:       50000,
			Type:        financialsheet.CategoryTypeIncome,
		},
	}

	// Mock the financial sheet service to return these transactions
	suite.mockFinancialSheetService.On("FindAllTransactions", ctx, suite.testUserID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int")).Return(transactions, nil)

	// Mock repository to return NotFound (no existing financial map)
	notFoundErr := errors.New(errors.Repository, "financial map not found", errors.NotFound, nil)
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(nil, notFoundErr)
	suite.mockRepo.On("SaveFinancialMap", ctx, mock.AnythingOfType("*dashboard.FinancialMap")).Return(nil)

	// Call the method
	financialMap, err := suite.service.FindFinancialMap(ctx, suite.testUserID)

	// Verify results
	suite.Require().NoError(err)
	suite.Require().NotNil(financialMap)
	suite.Require().Len(financialMap.IncomeSources, 2) // Should have 2 distinct income sources

	// Verify the income sources are correctly aggregated by money source name
	incomeSourcesByName := make(map[string]dashboard.IncomeSource)
	for _, source := range financialMap.IncomeSources {
		incomeSourcesByName[source.Name] = source
	}

	// Check "Bolsa Família" aggregation (3 transactions of 10000 each = 30000 total)
	bolsaFamilia, exists := incomeSourcesByName["Bolsa Família"]
	suite.True(exists, "Should have 'Bolsa Família' income source")
	suite.Equal("Bolsa Família", bolsaFamilia.Name)
	suite.Equal(monetary.Amount(30000), bolsaFamilia.MonthlyAmount)
	suite.Equal(financialsheet.MoneySourceOpt2, bolsaFamilia.MoneySource)

	// Check "Salário CLT" (1 transaction of 50000)
	salarioCLT, exists := incomeSourcesByName["Salário CLT"]
	suite.True(exists, "Should have 'Salário CLT' income source")
	suite.Equal("Salário CLT", salarioCLT.Name)
	suite.Equal(monetary.Amount(50000), salarioCLT.MonthlyAmount)
	suite.Equal(financialsheet.MoneySourceOpt1, salarioCLT.MoneySource)

	// Verify total monthly income
	suite.Equal(monetary.Amount(80000), financialMap.MonthlyIncome)

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetService.AssertExpectations(suite.T())
}

func TestIsNotFoundError(t *testing.T) {
	// Test with NotFound error
	notFoundErr := errors.New(errors.Repository, "not found", errors.NotFound, nil)
	assert.True(t, isNotFoundError(notFoundErr))

	// Test with other error
	internalErr := errors.New(errors.Repository, "internal error", errors.Internal, nil)
	assert.False(t, isNotFoundError(internalErr))

	// Test with non-domain error
	regularErr := assert.AnError
	assert.False(t, isNotFoundError(regularErr))
}
